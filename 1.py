#!/usr/bin/env python3
"""
SVG to PNG converter with quality options
"""

import argparse
import sys
from pathlib import Path

try:
    import cairosvg
    CAIRO_AVAILABLE = True
except ImportError:
    CAIRO_AVAILABLE = False

try:
    from PIL import Image
    import io
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from wand.image import Image as WandImage
    WAND_AVAILABLE = True
except ImportError:
    WAND_AVAILABLE = False


def convert_with_cairosvg(svg_path, png_path, dpi=300):
    """使用cairosvg转换SVG到PNG"""
    try:
        cairosvg.svg2png(
            url=str(svg_path),
            write_to=str(png_path),
            dpi=dpi
        )
        print(f"✅ 使用cairosvg成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
        return True
    except Exception as e:
        print(f"❌ cairosvg转换失败: {e}")
        return False


def convert_with_wand(svg_path, png_path, dpi=300):
    """使用Wand (ImageMagick)转换SVG到PNG"""
    try:
        with WandImage(filename=str(svg_path), resolution=dpi) as img:
            img.format = 'png'
            img.save(filename=str(png_path))
        print(f"✅ 使用Wand成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
        return True
    except Exception as e:
        print(f"❌ Wand转换失败: {e}")
        return False


def get_quality_settings():
    """获取预设的质量设置"""
    return {
        'low': {'dpi': 72, 'description': '低质量 (72 DPI) - 适合网页显示'},
        'medium': {'dpi': 150, 'description': '中等质量 (150 DPI) - 适合一般打印'},
        'high': {'dpi': 300, 'description': '高质量 (300 DPI) - 适合高质量打印'},
        'ultra': {'dpi': 600, 'description': '超高质量 (600 DPI) - 适合专业印刷'}
    }


def main():
    parser = argparse.ArgumentParser(
        description='将SVG文件转换为PNG格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
质量选项:
  low    - 72 DPI  (适合网页显示)
  medium - 150 DPI (适合一般打印)
  high   - 300 DPI (适合高质量打印)
  ultra  - 600 DPI (适合专业印刷)

示例:
  python svg_to_png.py 1.svg                    # 使用默认高质量
  python svg_to_png.py 1.svg -q medium          # 使用中等质量
  python svg_to_png.py 1.svg -d 450             # 自定义DPI
  python svg_to_png.py 1.svg -o output.png      # 指定输出文件名
        """
    )
    
    parser.add_argument('input', help='输入的SVG文件路径')
    parser.add_argument('-o', '--output', help='输出的PNG文件路径 (默认: 输入文件名.png)')
    parser.add_argument('-q', '--quality', 
                       choices=['low', 'medium', 'high', 'ultra'],
                       default='high',
                       help='输出质量 (默认: high)')
    parser.add_argument('-d', '--dpi', type=int, 
                       help='自定义DPI值 (覆盖质量预设)')
    parser.add_argument('--method', 
                       choices=['auto', 'cairosvg', 'wand'],
                       default='auto',
                       help='转换方法 (默认: auto)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    svg_path = Path(args.input)
    if not svg_path.exists():
        print(f"❌ 错误: 文件不存在 - {svg_path}")
        sys.exit(1)
    
    if svg_path.suffix.lower() != '.svg':
        print(f"❌ 错误: 输入文件不是SVG格式 - {svg_path}")
        sys.exit(1)
    
    # 确定输出文件路径
    if args.output:
        png_path = Path(args.output)
    else:
        png_path = svg_path.with_suffix('.png')
    
    # 确定DPI
    if args.dpi:
        dpi = args.dpi
        print(f"📐 使用自定义DPI: {dpi}")
    else:
        quality_settings = get_quality_settings()
        dpi = quality_settings[args.quality]['dpi']
        print(f"📐 使用{args.quality}质量: {quality_settings[args.quality]['description']}")
    
    # 检查可用的转换库
    available_methods = []
    if CAIRO_AVAILABLE:
        available_methods.append('cairosvg')
    if WAND_AVAILABLE:
        available_methods.append('wand')
    
    if not available_methods:
        print("❌ 错误: 没有找到可用的转换库!")
        print("\n请安装以下库之一:")
        print("  pip install cairosvg")
        print("  pip install Wand")
        print("\n注意: Wand需要先安装ImageMagick")
        sys.exit(1)
    
    print(f"🔧 可用的转换方法: {', '.join(available_methods)}")
    
    # 执行转换
    success = False
    
    if args.method == 'cairosvg' or (args.method == 'auto' and 'cairosvg' in available_methods):
        if CAIRO_AVAILABLE:
            success = convert_with_cairosvg(svg_path, png_path, dpi)
        else:
            print("❌ cairosvg不可用")
    
    if not success and (args.method == 'wand' or (args.method == 'auto' and 'wand' in available_methods)):
        if WAND_AVAILABLE:
            success = convert_with_wand(svg_path, png_path, dpi)
        else:
            print("❌ Wand不可用")
    
    if success:
        # 显示文件信息
        file_size = png_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        print(f"📁 输出文件大小: {size_mb:.2f} MB")
        print(f"🎯 转换完成!")
    else:
        print("❌ 转换失败!")
        sys.exit(1)


if __name__ == '__main__':
    main()