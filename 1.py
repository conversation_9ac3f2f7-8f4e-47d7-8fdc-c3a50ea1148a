#!/usr/bin/env python3
"""
SVG to PNG converter with quality options
"""

import argparse
import sys
from pathlib import Path

try:
    import cairosvg
    CAIRO_AVAILABLE = True
except (ImportError, OSError) as e:
    CAIRO_AVAILABLE = False
    CAIRO_ERROR = str(e)

try:
    from PIL import Image
    import io
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from wand.image import Image as WandImage
    WAND_AVAILABLE = True
except ImportError:
    WAND_AVAILABLE = False

try:
    import subprocess
    SUBPROCESS_AVAILABLE = True
except ImportError:
    SUBPROCESS_AVAILABLE = False


def convert_with_cairosvg(svg_path, png_path, dpi=300):
    """使用cairosvg转换SVG到PNG"""
    try:
        cairosvg.svg2png(
            url=str(svg_path),
            write_to=str(png_path),
            dpi=dpi
        )
        print(f"✅ 使用cairosvg成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
        return True
    except Exception as e:
        print(f"❌ cairosvg转换失败: {e}")
        return False


def convert_with_wand(svg_path, png_path, dpi=300):
    """使用Wand (ImageMagick)转换SVG到PNG"""
    try:
        with WandImage(filename=str(svg_path), resolution=dpi) as img:
            img.format = 'png'
            img.save(filename=str(png_path))
        print(f"✅ 使用Wand成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
        return True
    except Exception as e:
        print(f"❌ Wand转换失败: {e}")
        return False


def convert_with_pil_svg(svg_path, png_path, dpi=300):
    """使用PIL和svglib转换SVG到PNG"""
    try:
        from reportlab.graphics import renderPM
        from svglib.svglib import svg2rlg

        # 读取SVG并转换为ReportLab图形
        drawing = svg2rlg(str(svg_path))

        # 渲染为PNG
        renderPM.drawToFile(drawing, str(png_path), fmt='PNG', dpi=dpi)

        print(f"✅ 使用PIL+svglib成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
        return True

    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("   请安装: pip install svglib reportlab")
        return False
    except Exception as e:
        print(f"❌ PIL+svglib转换失败: {e}")
        return False


def convert_with_simple_svg(svg_path, png_path, dpi=300):
    """使用简单的SVG转换方法（基于PIL）"""
    try:
        # 尝试直接用PIL打开SVG（某些简单的SVG可能支持）
        from PIL import Image

        # 读取SVG内容
        with open(svg_path, 'r', encoding='utf-8') as f:
            svg_content = f.read()

        # 提取SVG尺寸
        import re
        width_match = re.search(r'width="(\d+)"', svg_content)
        height_match = re.search(r'height="(\d+)"', svg_content)

        if width_match and height_match:
            width = int(width_match.group(1))
            height = int(height_match.group(1))

            # 根据DPI调整尺寸
            scale_factor = dpi / 72.0
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            print(f"📏 检测到SVG尺寸: {width}x{height}, 缩放后: {new_width}x{new_height}")

        print(f"❌ 简单SVG转换方法不支持复杂SVG文件")
        return False

    except Exception as e:
        print(f"❌ 简单SVG转换失败: {e}")
        return False


def convert_with_inkscape(svg_path, png_path, dpi=300):
    """使用Inkscape命令行转换SVG到PNG"""
    try:
        # 尝试使用Inkscape命令行
        cmd = [
            'inkscape',
            '--export-type=png',
            f'--export-dpi={dpi}',
            f'--export-filename={png_path}',
            str(svg_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print(f"✅ 使用Inkscape成功转换: {svg_path} -> {png_path} (DPI: {dpi})")
            return True
        else:
            print(f"❌ Inkscape转换失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Inkscape转换超时")
        return False
    except FileNotFoundError:
        print("❌ 未找到Inkscape程序")
        return False
    except Exception as e:
        print(f"❌ Inkscape转换失败: {e}")
        return False


def check_available_methods():
    """检查可用的转换方法"""
    methods = {}

    # 检查cairosvg
    if CAIRO_AVAILABLE:
        methods['cairosvg'] = "✅ CairoSVG (推荐)"
    else:
        methods['cairosvg'] = f"❌ CairoSVG 不可用"

    # 检查Wand
    if WAND_AVAILABLE:
        methods['wand'] = "✅ Wand/ImageMagick"
    else:
        methods['wand'] = "❌ Wand/ImageMagick 不可用"

    # 检查PIL+svglib
    try:
        from reportlab.graphics import renderPM
        from svglib.svglib import svg2rlg
        methods['pil_svg'] = "✅ PIL+svglib"
    except ImportError:
        methods['pil_svg'] = "❌ PIL+svglib 不可用"

    # 检查Inkscape
    try:
        result = subprocess.run(['inkscape', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            methods['inkscape'] = "✅ Inkscape"
        else:
            methods['inkscape'] = "❌ Inkscape 不可用"
    except:
        methods['inkscape'] = "❌ Inkscape 不可用"

    return methods


def get_quality_settings():
    """获取预设的质量设置"""
    return {
        'low': {'dpi': 72, 'description': '低质量 (72 DPI) - 适合网页显示'},
        'medium': {'dpi': 150, 'description': '中等质量 (150 DPI) - 适合一般打印'},
        'high': {'dpi': 300, 'description': '高质量 (300 DPI) - 适合高质量打印'},
        'ultra': {'dpi': 600, 'description': '超高质量 (600 DPI) - 适合专业印刷'}
    }


def interactive_mode():
    """交互式模式"""
    print("=" * 60)
    print("🎨 SVG转PNG转换器 - 交互式模式")
    print("=" * 60)

    while True:
        print("\n📁 请选择SVG文件:")

        # 显示当前目录的SVG文件
        current_dir = Path('.')
        svg_files = list(current_dir.glob('*.svg'))

        if svg_files:
            print("当前目录中的SVG文件:")
            for i, svg_file in enumerate(svg_files, 1):
                print(f"  {i}. {svg_file.name}")
            print(f"  {len(svg_files) + 1}. 输入其他文件路径")
            print(f"  0. 退出程序")

            try:
                choice = input("\n请选择 (输入数字): ").strip()

                if choice == '0':
                    print("👋 再见!")
                    break
                elif choice == str(len(svg_files) + 1):
                    svg_path = input("请输入SVG文件路径: ").strip()
                    svg_path = Path(svg_path)
                else:
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(svg_files):
                        svg_path = svg_files[choice_idx]
                    else:
                        print("❌ 无效选择，请重新输入")
                        continue
            except ValueError:
                print("❌ 请输入有效数字")
                continue
        else:
            svg_path = input("请输入SVG文件路径 (或输入 'q' 退出): ").strip()
            if svg_path.lower() == 'q':
                print("👋 再见!")
                break
            svg_path = Path(svg_path)

        # 检查文件是否存在
        if not svg_path.exists():
            print(f"❌ 文件不存在: {svg_path}")
            continue

        if svg_path.suffix.lower() != '.svg':
            print(f"❌ 文件不是SVG格式: {svg_path}")
            continue

        # 选择输出文件名
        print(f"\n📝 输出文件设置:")
        default_output = svg_path.with_suffix('.png')
        print(f"默认输出: {default_output}")

        custom_output = input("自定义输出文件名 (直接回车使用默认): ").strip()
        if custom_output:
            png_path = Path(custom_output)
        else:
            png_path = default_output

        # 选择质量设置
        print(f"\n🎯 质量设置:")
        quality_settings = get_quality_settings()

        print("预设质量选项:")
        for i, (key, value) in enumerate(quality_settings.items(), 1):
            print(f"  {i}. {key} - {value['description']}")
        print(f"  {len(quality_settings) + 1}. 自定义DPI")

        try:
            quality_choice = input("请选择质量 (输入数字): ").strip()
            quality_keys = list(quality_settings.keys())

            if quality_choice == str(len(quality_settings) + 1):
                dpi = int(input("请输入自定义DPI值: "))
                print(f"📐 使用自定义DPI: {dpi}")
            else:
                quality_idx = int(quality_choice) - 1
                if 0 <= quality_idx < len(quality_keys):
                    quality_key = quality_keys[quality_idx]
                    dpi = quality_settings[quality_key]['dpi']
                    print(f"📐 使用{quality_key}质量: {quality_settings[quality_key]['description']}")
                else:
                    print("❌ 无效选择，使用默认高质量")
                    dpi = quality_settings['high']['dpi']
        except ValueError:
            print("❌ 输入无效，使用默认高质量")
            dpi = quality_settings['high']['dpi']

        # 执行转换
        success = perform_conversion(svg_path, png_path, dpi)

        if success:
            print(f"\n✅ 转换成功!")
            print(f"📁 输出文件: {png_path}")

            # 显示文件信息
            if png_path.exists():
                file_size = png_path.stat().st_size
                size_mb = file_size / (1024 * 1024)
                print(f"📊 文件大小: {size_mb:.2f} MB")
        else:
            print(f"\n❌ 转换失败!")

        # 询问是否继续
        print("\n" + "="*60)
        continue_choice = input("是否继续转换其他文件? (y/n): ").strip().lower()
        if continue_choice not in ['y', 'yes', '是']:
            print("👋 再见!")
            break


def perform_conversion(svg_path, png_path, dpi):
    """执行转换操作"""
    # 检查可用的转换方法
    methods = check_available_methods()

    print("🔧 转换方法状态:")
    for method, status in methods.items():
        print(f"  {status}")

    # 按优先级尝试转换
    success = False

    # 1. 优先尝试cairosvg
    if CAIRO_AVAILABLE:
        print("\n🚀 尝试使用CairoSVG转换...")
        success = convert_with_cairosvg(svg_path, png_path, dpi)

    # 2. 如果cairosvg失败，尝试PIL+svglib
    if not success:
        print("\n🚀 尝试使用PIL+svglib转换...")
        success = convert_with_pil_svg(svg_path, png_path, dpi)

    # 3. 如果失败，尝试Wand
    if not success and WAND_AVAILABLE:
        print("\n🚀 尝试使用Wand/ImageMagick转换...")
        success = convert_with_wand(svg_path, png_path, dpi)

    # 4. 如果前面都失败，尝试Inkscape
    if not success:
        print("\n🚀 尝试使用Inkscape转换...")
        success = convert_with_inkscape(svg_path, png_path, dpi)

    # 如果所有方法都失败
    if not success:
        print("\n❌ 所有转换方法都失败了!")
        print("\n💡 解决方案:")
        if not CAIRO_AVAILABLE:
            print("  1. 安装CairoSVG依赖:")
            print("     pip install cairosvg")
            print("     在Windows上还需要安装GTK+运行时或使用conda:")
            print("     conda install -c conda-forge cairo")

        print("  2. 安装PIL+svglib (推荐用于Windows):")
        print("     pip install svglib reportlab")

        if not WAND_AVAILABLE:
            print("  3. 安装Wand:")
            print("     pip install Wand")
            print("     然后安装ImageMagick: https://imagemagick.org/script/download.php")

        print("  4. 安装Inkscape:")
        print("     https://inkscape.org/release/")
        print("     确保Inkscape在系统PATH中")

    return success


def main():
    parser = argparse.ArgumentParser(
        description='将SVG文件转换为PNG格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
质量选项:
  low    - 72 DPI  (适合网页显示)
  medium - 150 DPI (适合一般打印)
  high   - 300 DPI (适合高质量打印)
  ultra  - 600 DPI (适合专业印刷)

示例:
  python 1.py 1.svg                    # 使用默认高质量
  python 1.py 1.svg -q medium          # 使用中等质量
  python 1.py 1.svg -d 450             # 自定义DPI
  python 1.py 1.svg -o output.png      # 指定输出文件名
  python 1.py --interactive            # 交互式模式
        """
    )

    parser.add_argument('input', nargs='?', help='输入的SVG文件路径')
    parser.add_argument('-o', '--output', help='输出的PNG文件路径 (默认: 输入文件名.png)')
    parser.add_argument('-q', '--quality',
                       choices=['low', 'medium', 'high', 'ultra'],
                       default='high',
                       help='输出质量 (默认: high)')
    parser.add_argument('-d', '--dpi', type=int,
                       help='自定义DPI值 (覆盖质量预设)')
    parser.add_argument('--method',
                       choices=['auto', 'cairosvg', 'wand'],
                       default='auto',
                       help='转换方法 (默认: auto)')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='启动交互式模式')

    args = parser.parse_args()

    # 如果没有输入文件且没有指定交互模式，则自动进入交互模式
    if not args.input and not args.interactive:
        print("🎯 未指定输入文件，自动进入交互式模式...")
        interactive_mode()
        return

    # 如果明确指定了交互模式
    if args.interactive:
        interactive_mode()
        return

    # 命令行模式
    command_line_mode(args)


def command_line_mode(args):
    """命令行模式"""
    # 检查输入文件
    svg_path = Path(args.input)
    if not svg_path.exists():
        print(f"❌ 错误: 文件不存在 - {svg_path}")
        sys.exit(1)

    if svg_path.suffix.lower() != '.svg':
        print(f"❌ 错误: 输入文件不是SVG格式 - {svg_path}")
        sys.exit(1)

    # 确定输出文件路径
    if args.output:
        png_path = Path(args.output)
    else:
        png_path = svg_path.with_suffix('.png')

    # 确定DPI
    if args.dpi:
        dpi = args.dpi
        print(f"📐 使用自定义DPI: {dpi}")
    else:
        quality_settings = get_quality_settings()
        dpi = quality_settings[args.quality]['dpi']
        print(f"📐 使用{args.quality}质量: {quality_settings[args.quality]['description']}")

    # 检查可用的转换库
    available_methods = []
    if CAIRO_AVAILABLE:
        available_methods.append('cairosvg')
    if WAND_AVAILABLE:
        available_methods.append('wand')

    if not available_methods:
        print("❌ 错误: 没有找到可用的转换库!")
        print("\n请安装以下库之一:")
        print("  pip install cairosvg")
        print("  pip install Wand")
        print("\n注意: Wand需要先安装ImageMagick")
        sys.exit(1)

    print(f"🔧 可用的转换方法: {', '.join(available_methods)}")

    # 执行转换
    success = False

    if args.method == 'cairosvg' or (args.method == 'auto' and 'cairosvg' in available_methods):
        if CAIRO_AVAILABLE:
            success = convert_with_cairosvg(svg_path, png_path, dpi)
        else:
            print("❌ cairosvg不可用")

    if not success and (args.method == 'wand' or (args.method == 'auto' and 'wand' in available_methods)):
        if WAND_AVAILABLE:
            success = convert_with_wand(svg_path, png_path, dpi)
        else:
            print("❌ Wand不可用")

    if success:
        # 显示文件信息
        file_size = png_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        print(f"📁 输出文件大小: {size_mb:.2f} MB")
        print(f"🎯 转换完成!")
    else:
        print("❌ 转换失败!")
        sys.exit(1)


if __name__ == '__main__':
    main()